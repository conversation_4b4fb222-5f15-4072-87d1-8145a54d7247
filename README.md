# 学生成绩管理系统

一个基于前后端分离架构的学生成绩管理系统，适合新手学习Web开发技术栈。

## 技术栈

### 后端
- **Go 1.21+** - 编程语言
- **Gin** - Web框架
- **GORM** - ORM框架
- **MySQL** - 数据库
- **JWT** - 身份认证

### 前端
- **Vue 3** - 前端框架
- **Element Plus** - UI组件库
- **Vite** - 构建工具
- **Pinia** - 状态管理
- **Axios** - HTTP客户端

## 功能特性

- 🔐 用户登录/注册
- 👥 学生信息管理（增删改查）
- 📚 课程信息管理
- 📊 成绩录入与管理
- 🔍 数据搜索与筛选
- 📄 分页显示
- 📱 响应式设计

## 项目结构

```
├── backend/                 # 后端代码
│   ├── main.go             # 程序入口
│   ├── config/             # 配置文件
│   ├── models/             # 数据模型
│   ├── controllers/        # 控制器
│   ├── middleware/         # 中间件
│   ├── routes/             # 路由
│   └── utils/              # 工具函数
└── frontend/               # 前端代码
    ├── src/
    │   ├── components/     # 组件
    │   ├── views/          # 页面
    │   ├── router/         # 路由
    │   ├── store/          # 状态管理
    │   ├── api/            # API接口
    │   └── utils/          # 工具函数
    ├── public/             # 静态资源
    └── package.json        # 依赖配置
```

## 快速开始

### 环境要求

- Go 1.21+
- Node.js 16+
- MySQL 8.0+

### 后端启动

1. 安装Go依赖
```bash
cd backend
go mod tidy
```

2. 配置数据库
编辑 `backend/config/database.go` 文件，修改数据库连接信息：
```go
dsn := "root:password@tcp(127.0.0.1:3306)/student_grade_system?charset=utf8mb4&parseTime=True&loc=Local"
```

3. 创建数据库
```sql
CREATE DATABASE student_grade_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

4. 启动后端服务
```bash
go run main.go
```

后端服务将在 `http://localhost:8000` 启动

### 前端启动

1. 安装依赖
```bash
cd frontend
npm install
```

2. 启动开发服务器
```bash
npm run dev
```

前端服务将在 `http://localhost:3000` 启动

## API接口

### 认证接口
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册

### 学生管理
- `GET /api/v1/students` - 获取学生列表
- `GET /api/v1/students/:id` - 获取学生详情
- `POST /api/v1/students` - 创建学生
- `PUT /api/v1/students/:id` - 更新学生
- `DELETE /api/v1/students/:id` - 删除学生

### 课程管理
- `GET /api/v1/courses` - 获取课程列表
- `GET /api/v1/courses/:id` - 获取课程详情
- `POST /api/v1/courses` - 创建课程
- `PUT /api/v1/courses/:id` - 更新课程
- `DELETE /api/v1/courses/:id` - 删除课程

### 成绩管理
- `GET /api/v1/grades` - 获取成绩列表
- `GET /api/v1/grades/:id` - 获取成绩详情
- `POST /api/v1/grades` - 创建成绩
- `PUT /api/v1/grades/:id` - 更新成绩
- `DELETE /api/v1/grades/:id` - 删除成绩

## 数据库设计

### 用户表 (users)
- id - 主键
- username - 用户名
- password - 密码（MD5加密）
- email - 邮箱
- role - 角色
- created_at/updated_at - 时间戳

### 学生表 (students)
- id - 主键
- student_no - 学号
- name - 姓名
- gender - 性别
- age - 年龄
- class - 班级
- major - 专业
- phone - 电话
- email - 邮箱
- address - 地址
- enroll_year - 入学年份
- status - 状态
- created_at/updated_at - 时间戳

### 课程表 (courses)
- id - 主键
- course_code - 课程代码
- course_name - 课程名称
- credits - 学分
- teacher - 授课教师
- department - 开课院系
- semester - 学期
- description - 课程描述
- status - 状态
- created_at/updated_at - 时间戳

### 成绩表 (grades)
- id - 主键
- student_id - 学生ID（外键）
- course_id - 课程ID（外键）
- score - 分数
- grade_level - 等级
- exam_type - 考试类型
- exam_date - 考试日期
- remarks - 备注
- created_at/updated_at - 时间戳

## 学习要点

### 后端学习要点
1. **Go语言基础** - 语法、包管理、并发
2. **Gin框架** - 路由、中间件、请求处理
3. **GORM** - 数据库操作、模型定义、关联查询
4. **JWT认证** - Token生成与验证
5. **RESTful API设计** - 接口规范、状态码

### 前端学习要点
1. **Vue 3** - 组合式API、响应式系统
2. **Element Plus** - 组件使用、主题定制
3. **Vue Router** - 路由配置、导航守卫
4. **Pinia** - 状态管理、数据持久化
5. **Axios** - HTTP请求、拦截器

## 扩展功能建议

- [ ] 数据导入导出
- [ ] 成绩统计图表
- [ ] 邮件通知功能
- [ ] 文件上传功能
- [ ] 权限管理系统
- [ ] 操作日志记录
- [ ] 数据备份恢复

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题，请提交 Issue 或联系开发者。
