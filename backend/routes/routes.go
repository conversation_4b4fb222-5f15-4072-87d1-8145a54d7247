package routes

import (
	"student-grade-system/controllers"
	"student-grade-system/middleware"

	"github.com/gin-gonic/gin"
)

// SetupRoutes 设置路由
func SetupRoutes(r *gin.Engine) {
	// API版本分组
	api := r.Group("/api/v1")

	// 公开路由（不需要认证）
	public := api.Group("/auth")
	{
		public.POST("/login", controllers.Login)
		public.POST("/register", controllers.Register)
	}

	// 需要认证的路由
	protected := api.Group("/")
	protected.Use(middleware.AuthMiddleware())
	{
		// 学生管理路由
		students := protected.Group("/students")
		{
			students.GET("", controllers.GetStudents)
			students.GET("/:id", controllers.GetStudent)
			students.POST("", controllers.CreateStudent)
			students.PUT("/:id", controllers.UpdateStudent)
			students.DELETE("/:id", controllers.DeleteStudent)
			students.GET("/:student_id/grades", controllers.GetStudentGrades)
		}

		// 课程管理路由
		courses := protected.Group("/courses")
		{
			courses.GET("", controllers.GetCourses)
			courses.GET("/:id", controllers.GetCourse)
			courses.POST("", controllers.CreateCourse)
			courses.PUT("/:id", controllers.UpdateCourse)
			courses.DELETE("/:id", controllers.DeleteCourse)
		}

		// 成绩管理路由
		grades := protected.Group("/grades")
		{
			grades.GET("", controllers.GetGrades)
			grades.GET("/:id", controllers.GetGrade)
			grades.POST("", controllers.CreateGrade)
			grades.PUT("/:id", controllers.UpdateGrade)
			grades.DELETE("/:id", controllers.DeleteGrade)
		}
	}
}
