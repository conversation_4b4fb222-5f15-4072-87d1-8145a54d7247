version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: student_grade_mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: student_grade_system
      MYSQL_USER: student_user
      MYSQL_PASSWORD: student_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: student_grade_backend
    ports:
      - "8000:8000"
    depends_on:
      - mysql
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USER: root
      DB_PASSWORD: password
      DB_NAME: student_grade_system
    volumes:
      - ./backend:/app
    working_dir: /app

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: student_grade_frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    working_dir: /app

volumes:
  mysql_data:
